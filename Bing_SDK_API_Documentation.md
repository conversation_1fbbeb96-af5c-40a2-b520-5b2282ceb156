# Bing Ads SDK API 接口文档

## 概述

已成功将 Bing 广告的 `findRpSearchLog` 方法从 SOAP 方式改为使用官方 SDK 方式调用。现在提供以下测试接口供您使用。

## 修改内容

### 1. 创建了 BingSdkService 类
- 位置：`nbchat-train-report/src/main/java/com/tydic/nbchat/train/report/promotion/bing/BingSdkService.java`
- 功能：使用 Bing Ads SDK 进行报告查询
- 主要方法：
  - `findSearchLogWithSdk()` - 使用 SDK 查询搜索日志
  - `validateSdkConfiguration()` - 验证 SDK 配置
  - `parseCsvFile()` - 解析下载的 CSV 报告文件

### 2. 修改了 BingAppService 类
- 集成了 BingSdkService
- 保留了 SOAP 方式作为备用方案
- 根据配置 `useSdk` 参数选择调用方式

### 3. 更新了配置文件
- 在 `application-local.yml` 中添加了 `useSdk: true` 配置
- 默认使用 SDK 方式调用

## API 接口

### 1. 测试 SDK 调用

**接口地址：** `GET /train/oauth/bing/test-sdk`

**请求参数：**
- `customerId` (String, 必填) - Bing Ads 客户ID

**示例请求：**
```
GET http://localhost:8705/train/oauth/bing/test-sdk?customerId=*********
```

**响应示例：**
```json
{
  "code": "200",
  "message": "成功",
  "data": {
    "configCheck": "应用配置检查通过",
    "tokenCheck": "访问令牌获取成功，长度: 1234",
    "developerTokenValidation": "成功: DeveloperToken验证通过",
    "accessTokenValidation": "成功: AccessToken验证通过",
    "customerIdValidation": "成功: CustomerId验证通过",
    "customerAccountIdValidation": "成功: CustomerAccountId验证通过",
    "overallValidation": "成功: 所有配置验证通过",
    "sdkTest": "SDK调用成功，返回记录数: 5"
  }
}
```

### 2. 查询搜索日志（使用 SDK）

**接口地址：** `GET /train/oauth/bing/search-logs`

**请求参数：**
- `customerId` (String, 必填) - Bing Ads 客户ID
- `startDate` (String, 必填) - 开始日期，格式：yyyy-MM-dd
- `endDate` (String, 必填) - 结束日期，格式：yyyy-MM-dd
- `promotionType` (String, 可选) - 推广类型，默认：ALL_PROMOTION_TYPE
  - 可选值：ALL_PROMOTION_TYPE, COMPUTER, MOBILE, TABLET
- `page` (Integer, 可选) - 页码，默认：1

**示例请求：**
```
GET http://localhost:8705/train/oauth/bing/search-logs?customerId=*********&startDate=2025-06-29&endDate=2025-06-30&promotionType=ALL_PROMOTION_TYPE&page=1
```

**响应示例：**
```json
{
  "code": "200",
  "message": "成功",
  "data": [
    {
      "id": null,
      "appType": "bing",
      "promotionType": "all",
      "dayData": "2025-06-29T00:00:00.000+00:00",
      "term": "AI培训",
      "keyword": "人工智能培训",
      "show": 1250,
      "click": 85,
      "consumption": 12500,
      "avgClickPrice": 147,
      "matchMode": "Broad"
    }
  ],
  "totalCount": 1
}
```

### 3. 其他辅助接口

#### 生成授权 URL
```
GET /train/oauth/bing/authUrl?customerId=*********
```

#### 检查授权状态
```
GET /train/oauth/bing/status?customerId=*********
```

#### 刷新访问令牌
```
POST /train/oauth/bing/refresh?customerId=*********
```

## 配置说明

### application-local.yml 配置
```yaml
nbchat-train:
  config:
    promotion:
      bing:
        limit: 300
        useSdk: true # 使用SDK方式调用（true: SDK, false: SOAP）
        reportDataCustomerId: *********
        redirectUri: http://localhost:8705/train/callback/bing
        appConfig:
          - customerId: *********
            developerToken: 1209Q0Z961153714
            customerAccountId: *********
            clientId: d5c38fb7-edd7-4793-a5fc-de9711aaf673
            clientSecret: ****************************************
            refreshToken: 
            appName: 课件帮
```

## SDK 依赖

项目已包含 Bing Ads SDK 依赖：
```xml
<dependency>
    <groupId>com.microsoft.bingads</groupId>
    <artifactId>microsoft.bingads</artifactId>
    <version>*********</version>
</dependency>
```

## 使用说明

1. **首次使用需要授权：**
   - 调用 `/train/oauth/bing/authUrl` 获取授权链接
   - 用户访问授权链接完成授权
   - 系统自动获取并保存访问令牌

2. **测试 SDK 功能：**
   - 调用 `/train/oauth/bing/test-sdk` 验证配置和测试 SDK 调用

3. **查询搜索日志：**
   - 调用 `/train/oauth/bing/search-logs` 获取实际的搜索日志数据

## 注意事项

1. **配置验证：** 确保所有必要的配置项都已正确填写
2. **令牌管理：** 访问令牌会自动刷新，无需手动处理
3. **错误处理：** 所有接口都包含详细的错误信息返回
4. **日志记录：** 所有 SDK 调用都有详细的日志记录，便于调试

## 故障排除

如果遇到问题，请检查：
1. 配置文件中的所有参数是否正确
2. 是否已完成 OAuth 授权
3. 访问令牌是否有效
4. 网络连接是否正常
5. 查看应用日志获取详细错误信息
