package com.tydic.nbchat.train.report.promotion.bing;

import com.microsoft.bingads.v13.reporting.*;
import com.microsoft.bingads.v13.reporting.ArrayOfSearchQueryReportColumn;
import com.microsoft.bingads.v13.reporting.SearchQueryReportColumn;
import com.microsoft.bingads.v13.reporting.SearchQueryReportRequest;
import com.microsoft.bingads.v13.reporting.ReportTime;
import com.microsoft.bingads.v13.reporting.Date;
import com.microsoft.bingads.v13.reporting.ReportFormat;
import com.microsoft.bingads.v13.reporting.ReportAggregation;
import com.microsoft.bingads.v13.reporting.SearchQueryReportFilter;
import com.microsoft.bingads.v13.reporting.DeviceTypeReportFilter;
import com.microsoft.bingads.v13.reporting.ArrayOfDeviceTypeReportFilter;
import com.microsoft.bingads.AuthenticationToken;
import com.microsoft.bingads.CustomerManagementExampleHelper;
import com.microsoft.bingads.ServiceClient;
import com.microsoft.bingads.reporting.ReportingServiceManager;
import com.tydic.nbchat.train.mapper.po.RpSearchLog;
import com.tydic.nbchat.train.report.promotion.bing.bo.ApplicationBO;
import com.tydic.nbchat.train.report.promotion.bo.PromotionTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/6/30
 * @description Bing广告SDK服务
 */
@Slf4j
@Service
public class BingSdkService {

    /**
     * 使用SDK方式查找搜索日志记录
     */
    public List<RpSearchLog> findSearchLogWithSdk(ApplicationBO application, String accessToken,
                                                  java.util.Date startDate, java.util.Date endDate,
                                                  PromotionTypeEnum promotionType, int limit) {
        try {
            log.info("【Bing SDK】开始使用SDK查询搜索日志，customerId: {}, 日期范围: {} 到 {}",
                    application.getCustomerId(), startDate, endDate);

            // 创建认证令牌
            AuthenticationToken authToken = new AuthenticationToken();
            authToken.setDeveloperToken(application.getDeveloperToken());
            authToken.setOAuthTokens(accessToken, null);

            // 创建报告服务管理器
            ReportingServiceManager reportingServiceManager = new ReportingServiceManager(authToken);
            reportingServiceManager.setCustomerId(Long.parseLong(application.getCustomerId()));
            reportingServiceManager.setCustomerAccountId(Long.parseLong(application.getCustomerAccountId()));

            // 创建搜索查询报告请求
            SearchQueryReportRequest reportRequest = buildSearchQueryReportRequest(startDate, endDate, promotionType);

            log.info("【Bing SDK】提交报告请求");
            
            // 提交报告请求并等待完成
            File reportFile = reportingServiceManager.submitAndDownload(reportRequest, null);
            
            log.info("【Bing SDK】报告下载完成，文件路径: {}", reportFile.getAbsolutePath());

            // 解析CSV文件
            List<RpSearchLog> searchLogs = parseCsvFile(reportFile, promotionType);
            
            // 清理临时文件
            if (reportFile.exists()) {
                reportFile.delete();
            }

            log.info("【Bing SDK】解析完成，共获取 {} 条记录", searchLogs.size());
            return searchLogs.size() > limit ? searchLogs.subList(0, limit) : searchLogs;

        } catch (Exception e) {
            log.error("【Bing SDK】查询搜索日志失败", e);
            throw new RuntimeException("SDK查询失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建搜索查询报告请求
     */
    private SearchQueryReportRequest buildSearchQueryReportRequest(java.util.Date startDate, java.util.Date endDate,
                                                                   PromotionTypeEnum promotionType) {
        SearchQueryReportRequest reportRequest = new SearchQueryReportRequest();
        
        // 设置报告格式
        reportRequest.setFormat(ReportFormat.CSV);
        reportRequest.setReportName("SearchQueryReport_" + System.currentTimeMillis());
        reportRequest.setReturnOnlyCompleteData(false);
        reportRequest.setAggregation(ReportAggregation.DAILY);

        // 设置时间范围
        ReportTime reportTime = new ReportTime();
        reportTime.setCustomDateRangeStart(convertToReportDate(startDate));
        reportTime.setCustomDateRangeEnd(convertToReportDate(endDate));
        reportRequest.setTime(reportTime);

        // 设置列
        ArrayOfSearchQueryReportColumn columns = new ArrayOfSearchQueryReportColumn();
        columns.getSearchQueryReportColumns().add(SearchQueryReportColumn.TIME_PERIOD);
        columns.getSearchQueryReportColumns().add(SearchQueryReportColumn.SEARCH_QUERY);
        columns.getSearchQueryReportColumns().add(SearchQueryReportColumn.IMPRESSIONS);
        columns.getSearchQueryReportColumns().add(SearchQueryReportColumn.CLICKS);
        columns.getSearchQueryReportColumns().add(SearchQueryReportColumn.SPEND);
        columns.getSearchQueryReportColumns().add(SearchQueryReportColumn.AVERAGE_CPC);
        columns.getSearchQueryReportColumns().add(SearchQueryReportColumn.KEYWORD);
        columns.getSearchQueryReportColumns().add(SearchQueryReportColumn.MATCH_TYPE);
        columns.getSearchQueryReportColumns().add(SearchQueryReportColumn.DEVICE_TYPE);
        reportRequest.setColumns(columns);

        // 设置过滤器（如果需要）
        if (promotionType != null && !PromotionTypeEnum.ALL_PROMOTION_TYPE.equals(promotionType)) {
            SearchQueryReportFilter filter = new SearchQueryReportFilter();
            ArrayOfDeviceTypeReportFilter deviceFilters = new ArrayOfDeviceTypeReportFilter();
            deviceFilters.getDeviceTypeReportFilters().add(getDeviceTypeFilter(promotionType));
            filter.setDeviceType(deviceFilters);
            reportRequest.setFilter(filter);
        }

        return reportRequest;
    }

    /**
     * 转换日期格式
     */
    private Date convertToReportDate(java.util.Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        
        Date reportDate = new Date();
        reportDate.setDay(cal.get(Calendar.DAY_OF_MONTH));
        reportDate.setMonth(cal.get(Calendar.MONTH) + 1); // Calendar月份从0开始
        reportDate.setYear(cal.get(Calendar.YEAR));
        
        return reportDate;
    }

    /**
     * 获取设备类型过滤器
     */
    private DeviceTypeReportFilter getDeviceTypeFilter(PromotionTypeEnum promotionType) {
        switch (promotionType) {
            case COMPUTER:
                return DeviceTypeReportFilter.COMPUTER;
            case MOBILE:
                return DeviceTypeReportFilter.SMARTPHONE;
            case TABLET:
                return DeviceTypeReportFilter.TABLET;
            default:
                return DeviceTypeReportFilter.COMPUTER;
        }
    }

    /**
     * 解析CSV文件
     */
    private List<RpSearchLog> parseCsvFile(File csvFile, PromotionTypeEnum promotionType) {
        List<RpSearchLog> searchLogs = new ArrayList<>();
        
        try (BufferedReader reader = new BufferedReader(new FileReader(csvFile))) {
            String line;
            boolean isFirstLine = true;
            String[] headers = null;
            
            while ((line = reader.readLine()) != null) {
                if (isFirstLine) {
                    headers = line.split(",");
                    isFirstLine = false;
                    continue;
                }
                
                String[] values = line.split(",");
                if (values.length >= headers.length) {
                    RpSearchLog searchLog = parseCsvRow(headers, values, promotionType);
                    if (searchLog != null) {
                        searchLogs.add(searchLog);
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("【Bing SDK】解析CSV文件失败", e);
            throw new RuntimeException("解析CSV文件失败: " + e.getMessage(), e);
        }
        
        return searchLogs;
    }

    /**
     * 解析CSV行数据
     */
    private RpSearchLog parseCsvRow(String[] headers, String[] values, PromotionTypeEnum promotionType) {
        try {
            RpSearchLog searchLog = new RpSearchLog();
            
            // 创建列索引映射
            Map<String, Integer> columnIndex = new HashMap<>();
            for (int i = 0; i < headers.length; i++) {
                columnIndex.put(headers[i].trim().replace("\"", ""), i);
            }
            
            // 设置基本信息
            searchLog.setAppType("bing");
            searchLog.setPromotionType(promotionType != null ? promotionType.getCode() : "all");
            
            // 解析时间
            String timePeriod = getColumnValue(columnIndex, values, "TimePeriod");
            if (StringUtils.isNotBlank(timePeriod)) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    searchLog.setDayData(sdf.parse(timePeriod.replace("\"", "")));
                } catch (Exception e) {
                    log.warn("【Bing SDK】解析日期失败: {}", timePeriod);
                    searchLog.setDayData(new java.util.Date());
                }
            }
            
            // 设置搜索词
            searchLog.setTerm(getColumnValue(columnIndex, values, "SearchQuery"));
            
            // 设置关键词
            searchLog.setKeyword(getColumnValue(columnIndex, values, "Keyword"));
            
            // 设置展现量
            String impressions = getColumnValue(columnIndex, values, "Impressions");
            searchLog.setShow(StringUtils.isNotBlank(impressions) ? Integer.parseInt(impressions.replace("\"", "")) : 0);
            
            // 设置点击量
            String clicks = getColumnValue(columnIndex, values, "Clicks");
            searchLog.setClick(StringUtils.isNotBlank(clicks) ? Integer.parseInt(clicks.replace("\"", "")) : 0);
            
            // 设置消费（转换为分）
            String spend = getColumnValue(columnIndex, values, "Spend");
            if (StringUtils.isNotBlank(spend)) {
                BigDecimal spendDecimal = new BigDecimal(spend.replace("\"", ""));
                searchLog.setConsumption(spendDecimal.multiply(BigDecimal.valueOf(100)).intValue());
            } else {
                searchLog.setConsumption(0);
            }
            
            // 设置平均点击价格（转换为分）
            String avgCpc = getColumnValue(columnIndex, values, "AverageCpc");
            if (StringUtils.isNotBlank(avgCpc)) {
                BigDecimal avgCpcDecimal = new BigDecimal(avgCpc.replace("\"", ""));
                searchLog.setAvgClickPrice(avgCpcDecimal.multiply(BigDecimal.valueOf(100)).intValue());
            } else {
                searchLog.setAvgClickPrice(0);
            }
            
            // 设置匹配模式
            searchLog.setMatchMode(getColumnValue(columnIndex, values, "MatchType"));
            
            return searchLog;
            
        } catch (Exception e) {
            log.warn("【Bing SDK】解析CSV行失败: {}", Arrays.toString(values), e);
            return null;
        }
    }

    /**
     * 获取列值
     */
    private String getColumnValue(Map<String, Integer> columnIndex, String[] values, String columnName) {
        Integer index = columnIndex.get(columnName);
        if (index != null && index < values.length) {
            return values[index].replace("\"", "").trim();
        }
        return null;
    }

    /**
     * 验证SDK配置
     */
    public Map<String, Object> validateSdkConfiguration(ApplicationBO application, String accessToken) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 验证必要参数
            if (StringUtils.isBlank(application.getDeveloperToken())) {
                result.put("developerTokenValidation", "失败: DeveloperToken为空");
                result.put("overallValidation", "失败: 配置验证未通过");
                return result;
            }
            result.put("developerTokenValidation", "成功: DeveloperToken验证通过");
            
            if (StringUtils.isBlank(accessToken)) {
                result.put("accessTokenValidation", "失败: AccessToken为空");
                result.put("overallValidation", "失败: 配置验证未通过");
                return result;
            }
            result.put("accessTokenValidation", "成功: AccessToken验证通过");
            
            if (StringUtils.isBlank(application.getCustomerId())) {
                result.put("customerIdValidation", "失败: CustomerId为空");
                result.put("overallValidation", "失败: 配置验证未通过");
                return result;
            }
            result.put("customerIdValidation", "成功: CustomerId验证通过");
            
            if (StringUtils.isBlank(application.getCustomerAccountId())) {
                result.put("customerAccountIdValidation", "失败: CustomerAccountId为空");
                result.put("overallValidation", "失败: 配置验证未通过");
                return result;
            }
            result.put("customerAccountIdValidation", "成功: CustomerAccountId验证通过");
            
            result.put("overallValidation", "成功: 所有配置验证通过");
            
        } catch (Exception e) {
            log.error("【Bing SDK】配置验证异常", e);
            result.put("overallValidation", "失败: 配置验证异常 - " + e.getMessage());
        }
        
        return result;
    }
}
