package com.tydic.nbchat.train.report.promotion.bing;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.ByteArrayInputStream;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/26
 * @description Bing广告报告工具类
 */
@Slf4j
public class BingReportUtils {

    /**
     * 从SOAP响应中提取报告请求ID
     */
    public static String extractReportRequestId(String soapResponse) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new ByteArrayInputStream(soapResponse.getBytes("UTF-8")));
            
            NodeList reportRequestIdNodes = document.getElementsByTagName("ReportRequestId");
            if (reportRequestIdNodes.getLength() > 0) {
                return reportRequestIdNodes.item(0).getTextContent();
            }
            
            log.warn("未在SOAP响应中找到ReportRequestId");
            return null;
            
        } catch (Exception e) {
            log.error("解析报告请求ID失败", e);
            return null;
        }
    }

    /**
     * 构建查询报告状态的SOAP请求
     */
    public static String buildPollGenerateReportRequest(String customerId, String developerToken, String reportRequestId) {
        StringBuilder soapBody = new StringBuilder();
        soapBody.append("<?xml version=\"1.0\" encoding=\"utf-8\"?>");
        soapBody.append("<s:Envelope xmlns:s=\"http://schemas.xmlsoap.org/soap/envelope/\">");
        soapBody.append("<s:Header>");
        soapBody.append("<h:ApplicationToken xmlns:h=\"https://bingads.microsoft.com/Reporting/v13\">")
                .append(developerToken).append("</h:ApplicationToken>");
        soapBody.append("<h:CustomerId xmlns:h=\"https://bingads.microsoft.com/Reporting/v13\">")
                .append(customerId).append("</h:CustomerId>");
        soapBody.append("</s:Header>");
        soapBody.append("<s:Body>");
        soapBody.append("<PollGenerateReportRequest xmlns=\"https://bingads.microsoft.com/Reporting/v13\">");
        soapBody.append("<ReportRequestId>").append(reportRequestId).append("</ReportRequestId>");
        soapBody.append("</PollGenerateReportRequest>");
        soapBody.append("</s:Body>");
        soapBody.append("</s:Envelope>");
        
        return soapBody.toString();
    }

    /**
     * 从轮询响应中提取报告状态和下载URL
     */
    public static ReportStatus extractReportStatus(String soapResponse) {
        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new ByteArrayInputStream(soapResponse.getBytes("UTF-8")));
            
            ReportStatus status = new ReportStatus();
            
            // 获取报告状态
            NodeList statusNodes = document.getElementsByTagName("ReportRequestStatus");
            if (statusNodes.getLength() > 0) {
                status.setStatus(statusNodes.item(0).getTextContent());
            }
            
            // 获取下载URL
            NodeList urlNodes = document.getElementsByTagName("ReportDownloadUrl");
            if (urlNodes.getLength() > 0) {
                status.setDownloadUrl(urlNodes.item(0).getTextContent());
            }
            
            return status;
            
        } catch (Exception e) {
            log.error("解析报告状态失败", e);
            return null;
        }
    }

    /**
     * 下载CSV报告数据
     */
    public static List<String[]> downloadCsvReport(String downloadUrl) {
        List<String[]> csvData = new ArrayList<>();
        
        try {
            URL url = new URL(downloadUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            
            BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String line;
            boolean isFirstLine = true;
            
            while ((line = reader.readLine()) != null) {
                // 跳过标题行
                if (isFirstLine) {
                    isFirstLine = false;
                    continue;
                }
                
                // 解析CSV行
                String[] fields = parseCsvLine(line);
                if (fields != null && fields.length > 0) {
                    csvData.add(fields);
                }
            }
            
            reader.close();
            connection.disconnect();
            
        } catch (Exception e) {
            log.error("下载CSV报告失败", e);
        }
        
        return csvData;
    }

    /**
     * 解析CSV行，处理引号和逗号
     */
    private static String[] parseCsvLine(String line) {
        if (StringUtils.isBlank(line)) {
            return null;
        }
        
        List<String> fields = new ArrayList<>();
        StringBuilder currentField = new StringBuilder();
        boolean inQuotes = false;
        
        for (int i = 0; i < line.length(); i++) {
            char c = line.charAt(i);
            
            if (c == '"') {
                inQuotes = !inQuotes;
            } else if (c == ',' && !inQuotes) {
                fields.add(currentField.toString().trim());
                currentField = new StringBuilder();
            } else {
                currentField.append(c);
            }
        }
        
        // 添加最后一个字段
        fields.add(currentField.toString().trim());
        
        return fields.toArray(new String[0]);
    }

    /**
     * 报告状态类
     */
    public static class ReportStatus {
        private String status;
        private String downloadUrl;

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getDownloadUrl() {
            return downloadUrl;
        }

        public void setDownloadUrl(String downloadUrl) {
            this.downloadUrl = downloadUrl;
        }

        public boolean isSuccess() {
            return "Success".equals(status);
        }

        public boolean isPending() {
            return "Pending".equals(status);
        }

        public boolean isError() {
            return "Error".equals(status);
        }
    }
}
