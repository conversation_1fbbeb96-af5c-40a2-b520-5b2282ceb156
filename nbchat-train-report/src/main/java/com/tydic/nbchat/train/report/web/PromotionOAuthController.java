package com.tydic.nbchat.train.report.web;

import com.tydic.nbchat.train.mapper.po.RpSearchLog;
import com.tydic.nbchat.train.report.promotion.bing.BingAppService;
import com.tydic.nbchat.train.report.promotion.bo.PromotionTypeEnum;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/26
 * @description 推广平台OAuth授权管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/train/oauth")
public class PromotionOAuthController {

    @Resource
    private BingAppService bingAppService;

    /**
     * 生成Bing OAuth授权URL
     * 
     * @param customerId Bing Ads客户ID
     * @return 包含授权URL的响应
     */
    @GetMapping("/bing/authUrl")
    public Rsp<String> generateBingAuthUrl(@RequestParam String customerId) {
        log.info("生成Bing OAuth授权URL，customerId: {}", customerId);
        
        if (StringUtils.isBlank(customerId)) {
            return BaseRspUtils.createErrorRsp("customerId不能为空");
        }
        
        try {
            String authUrl = bingAppService.generateAuthUrl(customerId);
            if (StringUtils.isBlank(authUrl)) {
                return BaseRspUtils.createErrorRsp("生成授权URL失败，请检查customerId配置");
            }
            
            return BaseRspUtils.createSuccessRsp(authUrl);
            
        } catch (Exception e) {
            log.error("生成Bing OAuth授权URL异常", e);
            return BaseRspUtils.createErrorRsp("生成授权URL异常: " + e.getMessage());
        }
    }
    
    /**
     * 检查Bing OAuth授权状态
     * 
     * @param customerId Bing Ads客户ID
     * @return 授权状态信息
     */
    @GetMapping("/bing/status")
    public Rsp<String> checkBingAuthStatus(@RequestParam String customerId) {
        log.info("检查Bing OAuth授权状态，customerId: {}", customerId);
        
        if (StringUtils.isBlank(customerId)) {
            return BaseRspUtils.createErrorRsp("customerId不能为空");
        }
        
        try {
            // 尝试获取访问令牌
            String accessToken = bingAppService.getAccessToken(customerId);
            if (StringUtils.isNotBlank(accessToken)) {
                return BaseRspUtils.createSuccessRsp("已授权");
            } else {
                return BaseRspUtils.createSuccessRsp("未授权");
            }
            
        } catch (Exception e) {
            log.error("检查Bing OAuth授权状态异常", e);
            return BaseRspUtils.createErrorRsp("检查授权状态异常: " + e.getMessage());
        }
    }
    
    /**
     * 手动刷新Bing访问令牌
     * 
     * @param customerId Bing Ads客户ID
     * @return 刷新结果
     */
    @PostMapping("/bing/refresh")
    public Rsp<String> refreshBingToken(@RequestParam String customerId) {
        log.info("手动刷新Bing访问令牌，customerId: {}", customerId);
        
        if (StringUtils.isBlank(customerId)) {
            return BaseRspUtils.createErrorRsp("customerId不能为空");
        }
        
        try {
            bingAppService.doRefreshToken(customerId);
            
            // 检查刷新后的令牌
            String accessToken = bingAppService.getAccessToken(customerId);
            if (StringUtils.isNotBlank(accessToken)) {
                return BaseRspUtils.createSuccessRsp("令牌刷新成功");
            } else {
                return BaseRspUtils.createErrorRsp("令牌刷新失败");
            }
            
        } catch (Exception e) {
            log.error("手动刷新Bing访问令牌异常", e);
            return BaseRspUtils.createErrorRsp("刷新令牌异常: " + e.getMessage());
        }
    }
}
